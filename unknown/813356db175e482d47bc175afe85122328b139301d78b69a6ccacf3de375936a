@import "selectize/dist/css/selectize";

.selectize-control:not(.multi) {
    .selectize-input .item,
    .selectize-dropdown .option {
        font-size: 14px;
    }

    .plugin-remove_button [data-value] {
        padding-right: 0 !important;
    }
}

.selectize-control {
    &.multi {
        .selectize-input {
            &.has-items {
                padding: 4px 4px 1px;
            }

            > div {
                padding: 3px 8px;
            }
        }
    }

    &.single .selectize-input {
        cursor: text;

        &.full {
            padding: 8px 12px;
        }

        &:after {
            content: none;
        }

        > span {
            display: flex;
            padding: 2px 0;
        }

        input {
            cursor: text;
            position: absolute;
            left: -10000px;
        }
    }

    &.plugin-remove_button {
        [data-value] .remove {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0;
            border-left-color: #e9e9e9;
        }

        .remove-single {
            top: 1px;
            color: #333333;
            text-decoration: none;
        }
    }
}

.selectize-input {
    padding: 4px 12px;
    border-radius: 6px;
    border-color: #d9d9d9;
    min-height: 36px;
    vertical-align: bottom;
    box-shadow: none !important;
    transition: border-color ease-in-out 0.15s;

    &.dropdown-active {
        border-radius: 6px 6px 0 0;
    }

    .dropdown-active {
        -webkit-border-radius: 0;
        border-radius: 0;
    }

    input {
        font-size: 15px;
        transition: 0ms !important;
    }

    input::-moz-placeholder {
        color: #999999;
        opacity: 1;
    }

    input:-ms-input-placeholder {
        color: #999999;
    }

    input::-webkit-input-placeholder {
        color: #999999;
    }

    &.focus {
        border-color: #6f8dfd;
        box-shadow: 0 0 2px rgba(30, 140, 190, 0.8);
    }

    .item {
        border-radius: 3px;
        float: left;
        white-space: normal;
        word-break: break-all;
    }
}

.ltr {
    .selectize-input {
        > input {
            margin-top: 4px !important;
        }
    }
}

.selectize-dropdown {
    border-radius: 0 0 6px 6px;
    z-index: 11;
    overflow: hidden;

    [data-selectable] {
        cursor: pointer;
    }

    .active {
        background-color: #f9f9f9;
    }

    .selectize-dropdown-content {
        .option {
            &.selected {
                color: #a6a6a6;
            }
        }

        .create strong {
            font-family: "Inter", sans-serif;
            font-weight: 600;
        }
    }
}
