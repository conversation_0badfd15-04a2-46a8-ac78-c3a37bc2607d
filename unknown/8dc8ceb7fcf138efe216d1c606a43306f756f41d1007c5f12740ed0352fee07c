/* buttons */

.btn {
    font-size: 14px;
    font-weight: 500;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    transition: 150ms ease-in-out;
}

.btn-default {
    background: #f1f1f1;
    outline: 0;
    border-color: #dddddd;

    &.focus,
    &:focus,
    &.active {
        background: #f1f1f1;
        outline: 0;
        border-color: #dddddd;
    }

    &:active {
        background: #f1f1f1;
        outline: 0;
        border-color: #dddddd;

        &:hover {
            background: #f1f1f1;
            outline: 0;
            border-color: #dddddd;
        }
    }

    &.active:hover,
    &:active:focus,
    &.active:focus,
    &:active.focus,
    &.active.focus {
        background: #f1f1f1;
        outline: 0;
        border-color: #dddddd;
    }

    &.disabled {
        &:hover,
        &.focus,
        &:focus {
            background: #f1f1f1;
            outline: 0;
            border-color: #dddddd;
        }
    }

    &[disabled] {
        &:focus,
        &:hover,
        &.focus,
        &.active,
        &:active {
            background: #f1f1f1;
            outline: 0;
            border-color: #dddddd;
        }
    }
}

.btn-delete {
    margin-left: 10px;
}

fieldset[disabled] .btn-default {
    &:focus,
    &.focus,
    &:hover {
        background: #f1f1f1;
        outline: 0;
        border-color: #dddddd;
    }
}

.open > .dropdown-toggle.btn-default {
    background: #f1f1f1;
    outline: 0;
    border-color: #dddddd;

    &:focus,
    &.focus,
    &:hover {
        background: #f1f1f1;
        outline: 0;
        border-color: #dddddd;
    }
}

.btn-default:hover {
    border-color: #dddddd;
    background: #e7e7e7;
}

.btn-primary {
    background: #0068e1;
    outline: 0;

    &.focus,
    &:focus,
    &:active,
    &.active,
    &:active:hover,
    &.active:hover,
    &:active:focus,
    &.active:focus,
    &:active.focus,
    &.active.focus {
        background: #0068e1;
        outline: 0;
    }

    &.disabled {
        &:hover,
        &.focus,
        &:focus {
            background: #0068e1;
            outline: 0;
        }
    }

    &[disabled] {
        &:focus,
        &:hover,
        &.focus,
        &.active,
        &:active {
            background: #0068e1;
            outline: 0;
        }
    }
}

fieldset[disabled] .btn-primary {
    &:focus,
    &.focus,
    &:hover {
        background: #0068e1;
        outline: 0;
    }
}

.open > .dropdown-toggle.btn-primary {
    background: #0068e1;
    outline: 0;

    &:focus,
    &.focus,
    &:hover {
        background: #0068e1;
        outline: 0;
    }
}

.btn-primary:hover {
    background: #0059bd;
}

.btn-danger {
    background: #fc4b4b;

    &.focus,
    &:focus,
    &.active {
        background: #fc4b4b;
    }

    &:active {
        background: #fc4b4b;

        &:hover {
            background: #fc4b4b;
        }
    }

    &.active:hover,
    &:active:focus,
    &.active:focus,
    &:active.focus,
    &.active.focus {
        background: #fc4b4b;
    }

    &.disabled {
        &:hover,
        &.focus,
        &:focus {
            background: #fc4b4b;
        }
    }

    &[disabled] {
        &:focus,
        &:hover,
        &.focus,
        &.active,
        &:active {
            background: #fc4b4b;
        }
    }
}

fieldset[disabled] .btn-danger {
    &:focus,
    &.focus,
    &:hover {
        background: #fc4b4b;
    }
}

.open > .dropdown-toggle.btn-danger {
    background: #fc4b4b;
    &:focus,
    &.focus,
    &:hover {
        background: #fc4b4b;
    }
}

.btn-danger:hover {
    background: #ff7070;
}

.btn-info {
    background: #4bcffc;

    &.focus,
    &:focus,
    &.active {
        background: #4bcffc;
    }

    &:active {
        background: #4bcffc;

        &:hover {
            background: #4bcffc;
        }
    }

    &.active:hover,
    &:active:focus,
    &.active:focus,
    &:active.focus,
    &.active.focus {
        background: #4bcffc;
    }

    &.disabled {
        &:hover,
        &.focus,
        &:focus {
            background: #4bcffc;
        }
    }

    &[disabled] {
        &:focus,
        &:hover,
        &.focus,
        &.active,
        &:active {
            background: #4bcffc;
        }
    }
}

fieldset[disabled] .btn-info {
    &:focus,
    &.focus,
    &:hover {
        background: #4bcffc;
    }
}

.open > .dropdown-toggle.btn-info {
    background: #4bcffc;

    &:focus,
    &.focus,
    &:hover {
        background: #4bcffc;
    }
}

.btn-info:hover {
    background: #6fcffd;
}

.btn {
    &:focus,
    &:hover:focus,
    &:active:focus {
        outline: 0;
        box-shadow: none;
    }
}

.btn-loading {
    position: relative;
    color: transparent !important;

    &:after {
        position: absolute;
        content: "";
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        margin: auto;
        height: 14px;
        width: 14px;
        border: 1px solid #ffffff;
        border-radius: 100%;
        border-right-color: transparent;
        border-top-color: transparent;
        animation: spinAround 600ms infinite linear;
    }

    &.btn-default:after {
        border: 1px solid #0068e1;
        border-right-color: transparent;
        border-top-color: transparent;
    }
}

@keyframes spinAround {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(359deg);
    }
}

/* bg color */

.bg-black {
    background: #494f5a;
}

.bg-red {
    background: #fc4b4b;
}

.bg-green {
    background: #37bc9b;
}

.bg-blue {
    background: #0068e1;
}

/* text */

.text-black {
    color: #494f5a;
}

.text-red {
    color: #fc4b4b;

    &:hover,
    &:focus {
        color: #fc4b4b;
    }
}

.text-green {
    color: #37bc9b;
}

.text-blue {
    color: #0068e1;
}

/* label */

.label {
    font-weight: 500;
    display: inline-block;
    padding: 5px 10px;
}

.label-default {
    color: #4b5563;
    background: #e5e7eb;
}

.label-primary {
    color: #3b82f6;
    background: #dbeafe;
}

.label-success {
    color: #16a34a;
    background: #bbf7d0;
}

.label-danger {
    color: #ef4444;
    background: #fee2e2;
}

.label-warning {
    color: #c08304;
    background: #fdeba3;
}

/* form error */

.has-error {
    .help-block,
    .control-label,
    .radio,
    .checkbox,
    .radio-inline,
    .checkbox-inline,
    &.radio label,
    &.checkbox label,
    &.radio-inline label,
    &.checkbox-inline label {
        color: #ff3366;
    }

    .form-control {
        border-color: #ff3366;
        box-shadow: none;

        &:focus {
            border-color: #ff3366;
            box-shadow: none;
        }
    }

    .input-group-addon {
        color: #ff3366;
        background: #f6f6f7;
        border-color: #d9d9d9;
    }

    .form-control-feedback {
        color: #ff3366;
    }
}

.input-group-addon {
    background: #f6f6f7;
    border-color: #d9d9d9;
    border-radius: 6px;

    .fa-times {
        -webkit-text-stroke: 1px #f6f6f7;
    }
}

.checkbox {
    label {
        font-size: 14px;
        font-weight: 400 !important;
        color: #333333;
        margin-bottom: 0 !important;
    }

    [type="checkbox"] {
        &:checked,
        &:not(:checked) {
            position: absolute;
            display: none;
        }

        &:checked + label,
        &:not(:checked) + label {
            font-family: "Inter", sans-serif;
            position: relative;
            padding-left: 28px;
            cursor: pointer;
            display: inline-block;
            text-align: left;
        }

        &:checked + label:before,
        &:not(:checked) + label:before {
            content: "";
            position: absolute;
            left: 0;
            top: 1px;
            width: 17px;
            height: 17px;
            border-radius: 4px;
            background: #e9e9e9;
            transition: 150ms ease-in-out;
        }

        &:checked + label:after,
        &:not(:checked) + label:after {
            content: "\f00c";
            font-family: FontAwesome;
            font-size: 13px;
            position: absolute;
            top: 1px;
            left: 2px;
            color: #ffffff;
            -webkit-text-stroke: 1px #0068e1;
            transition: 150ms ease-in-out;
        }

        &:checked + label:before {
            background: #0068e1;
        }

        &:not(:checked) + label:after {
            opacity: 0;
            transform: scale(0);
        }

        &:checked + label:after {
            -webkit-text-stroke: 1px #0068e1;
            opacity: 1;
            transform: scale(1);
        }
    }
}

.radio {
    text-align: left;

    label {
        color: #333333;
    }

    [type="radio"] {
        &:checked,
        &:not(:checked) {
            position: absolute;
            left: -9999px;
        }

        &:checked + label,
        &:not(:checked) + label {
            font-family: "Inter", sans-serif;
            position: relative;
            padding-left: 28px;
            cursor: pointer;
            line-height: 22px;
            display: inline-block;
        }

        &:checked + label:before,
        &:not(:checked) + label:before {
            content: "";
            position: absolute;
            left: 0;
            top: 1px;
            width: 19px;
            height: 19px;
            border: 1px solid #d2d6de;
            border-radius: 100%;
            background: #ffffff;
        }

        &:checked + label:after {
            content: "";
            width: 13px;
            height: 13px;
            background: #0068e1;
            position: absolute;
            top: 4px;
            left: 3px;
            border-radius: 100%;
            transition: 150ms ease-in-out;
        }

        &:not(:checked) + label:after {
            content: "";
            width: 14px;
            height: 14px;
            background: #0068e1;
            position: absolute;
            top: 3px;
            left: 3px;
            border-radius: 100%;
            transition: 150ms ease-in-out;
            opacity: 0;
            transform: scale(0);
        }

        &:checked + label:after {
            opacity: 1;
            transform: scale(1);
        }
    }

    + .radio {
        margin-top: 0;
    }
}

.checkbox + .checkbox {
    margin-top: 0;
}

.switch {
    [type="checkbox"] {
        display: none;

        &:checked + label::before {
            background: #0068e1;
        }

        &:checked + label::after {
            left: 16px;
        }
    }

    label {
        font-weight: 400 !important;
        position: relative;
        min-height: 20px;
        margin-bottom: 0 !important;
        padding-left: 46px;
        cursor: pointer;

        &:before {
            content: "";
            position: absolute;
            left: 0;
            top: 4px;
            height: 12px;
            width: 30px;
            background: #e9e9e9;
            border-radius: 8px;
            transition: 150ms ease-in-out;
        }

        &:after {
            content: "";
            position: absolute;
            left: -4px;
            top: 1px;
            height: 18px;
            width: 18px;
            background: #ffffff;
            border-radius: 16px;
            box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.3);
            transition: 150ms ease-in-out;
        }
    }
}

/* select option */

.custom-select-white {
    appearance: none;
    background: #f9f9f9 url("../images/arrow-white.png") no-repeat right 8px
        center;
    background-size: 10px;
    line-height: normal !important;
    height: 40px;
    padding: 0 30px 0 10px;
    border-radius: 3px;
    border-color: #d9d9d9;
}

.custom-select-black {
    appearance: none;
    background: #ffffff url("../images/arrow-black.png") no-repeat right 8px
        center;
    background-size: 10px;
    height: 40px;
    padding: 0 30px 0 10px;
    border-radius: 3px;
    border-color: #d9d9d9;
}

.custom-select-white:focus,
.custom-select-black:focus {
    outline: 0;
}
