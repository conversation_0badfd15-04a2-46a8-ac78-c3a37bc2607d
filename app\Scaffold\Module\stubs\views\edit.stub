@extends('admin::layout')

@component('admin::components.page.header')
    @slot('title', trans('admin::resource.edit', ['resource' => trans('$LOWERCASE_MODULE_NAME$::$PLURAL_SNAKE_CASE_ENTITY_NAME$.$SNAKE_CASE_ENTITY_NAME$')]))
    @slot('subtitle', '')

    <li><a href="{{ route('admin.$PLURAL_SNAKE_CASE_ENTITY_NAME$.index') }}">{{ trans('$LOWERCASE_MODULE_NAME$::$PLURAL_SNAKE_CASE_ENTITY_NAME$.$PLURAL_SNAKE_CASE_ENTITY_NAME$') }}</a></li>
    <li class="active">{{ trans('admin::resource.edit', ['resource' => trans('$LOWERCASE_MODULE_NAME$::$PLURAL_SNAKE_CASE_ENTITY_NAME$.$SNAKE_CASE_ENTITY_NAME$')]) }}</li>
@endcomponent

@section('content')
    <form method="POST" action="{{ route('admin.$PLURAL_SNAKE_CASE_ENTITY_NAME$.update', $$LCFIRST_ENTITY_NAME$) }}" class="form-horizontal" id="$KEBAB_CASE_ENTITY_NAME$-edit-form" novalidate>
        {{ csrf_field() }}
        {{ method_field('put') }}
    </form>
@endsection

@include('$LOWERCASE_MODULE_NAME$::admin.$PLURAL_SNAKE_CASE_ENTITY_NAME$.partials.shortcuts')

@push('globals')
    @vite([
        'modules/Variation/Resources/assets/admin/sass/main.scss',
        'modules/Variation/Resources/assets/admin/js/main.js',
    ])
@endpush
